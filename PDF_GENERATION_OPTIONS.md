# PDF Generation Options - No Browser Path Required

This document outlines different approaches to generate PDFs from your CV without hardcoding browser paths.

## Current Implementation Issues
- Hardcoded Brave browser path: `/usr/bin/brave`
- Not portable across different systems
- Fails if Brave is not installed or in a different location

## Alternative Approaches

### Option 1: Default Chrome/Chromium Detection (IMPLEMENTED)
**File**: `src/main.go` (modified)
**Status**: ✅ Ready to use

**Pros**:
- Minimal changes to existing code
- Uses system's default Chrome/Chromium installation
- chromedp automatically finds the browser

**Cons**:
- Still requires Chrome/Chromium to be installed
- May have compatibility issues on some systems

**Usage**:
```bash
cd src && go run main.go
```

### Option 2: wkhtmltopdf (Native Binary)
**File**: `src/main_wkhtmltopdf.go`
**Status**: ✅ Ready to use (dependency already in go.mod)

**Pros**:
- No browser required
- Fast and reliable
- Excellent HTML/CSS support
- Already in your dependencies

**Cons**:
- Requires wkhtmltopdf binary to be installed on system
- External dependency

**Installation**:
```bash
# Ubuntu/Debian
sudo apt-get install wkhtmltopdf

# Arch Linux
sudo pacman -S wkhtmltopdf

# macOS
brew install wkhtmltopdf
```

**Usage**:
```bash
cd src && go run main_wkhtmltopdf.go
```

### Option 3: Rod (Modern Browser Automation)
**File**: `src/main_rod.go`
**Status**: 🔄 Requires dependency installation

**Pros**:
- More modern than chromedp
- Better browser detection
- Cleaner API
- Automatic browser management

**Cons**:
- Still requires Chrome/Chromium
- Additional dependency

**Installation**:
```bash
cd src && go get github.com/go-rod/rod
```

**Usage**:
```bash
cd src && go run main_rod.go
```

## Recommended Approach

### For Maximum Portability: Option 2 (wkhtmltopdf)
- No browser path issues
- Works on servers without GUI
- Excellent PDF quality
- Already in your dependencies

### For Minimal Changes: Option 1 (Modified chromedp)
- Smallest code change
- Leverages existing setup
- Good for development environments

## Implementation Steps

### To use wkhtmltopdf (Recommended):
1. Install wkhtmltopdf on your system
2. Rename current main.go to main_chromedp.go
3. Rename main_wkhtmltopdf.go to main.go
4. Test: `cd src && go run main.go`

### To use improved chromedp:
1. Current main.go is already updated
2. Test: `cd src && go run main.go`

### To use Rod:
1. Install dependency: `cd src && go get github.com/go-rod/rod`
2. Choose between main_rod.go or rename it to main.go
3. Test: `cd src && go run main.go`

## Testing All Options

You can test each approach:
```bash
# Test improved chromedp (current main.go)
cd src && go run main.go

# Test wkhtmltopdf
cd src && go run main_wkhtmltopdf.go

# Test Rod (after installing dependency)
cd src && go get github.com/go-rod/rod
cd src && go run main_rod.go
```

All approaches will generate the PDF in the `pdf/` directory.
