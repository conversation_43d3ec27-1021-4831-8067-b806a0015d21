package main

import (
	"fmt"
	"io"
	"log"
	"os"

	"github.com/Sebas<PERSON>anK<PERSON>/go-wkhtmltopdf"
)

func main() {
	// Suppress default log output
	log.SetOutput(io.Discard)

	// Get current directory
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting current directory: %v\n", err)
		os.Exit(1)
	}

	// Construct paths manually
	parentDir := currentDir[:len(currentDir)-len("/src")]
	cvHtmlPath := fmt.Sprintf("file://%s/web/index.html?type=pdf", parentDir)
	cvPdfDir := parentDir + "/pdf"
	cvPdfPath := cvPdfDir + "/Valon_Mulolli_CV.pdf"

	// Create pdf directory if it doesn't exist
	if err := os.MkdirAll(cvPdfDir, 0o755); err != nil {
		fmt.Fprintf(os.Stderr, "Error creating pdf directory: %v\n", err)
		os.Exit(1)
	}

	// Create a new PDF generator
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating PDF generator: %v\n", err)
		os.Exit(1)
	}

	// Set PDF options (equivalent to your chromedp settings)
	pdfg.Dpi.Set(300)
	pdfg.MarginTop.Set(10)    // 10mm
	pdfg.MarginBottom.Set(10) // 10mm
	pdfg.MarginLeft.Set(20)   // 20mm
	pdfg.MarginRight.Set(20)  // 20mm
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeA4)

	// Add the HTML page
	page := wkhtmltopdf.NewPage(cvHtmlPath)
	page.EnableLocalFileAccess.Set(true) // Allow access to local files
	pdfg.AddPage(page)

	// Generate PDF
	err = pdfg.Create()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error generating PDF: %v\n", err)
		os.Exit(1)
	}

	// Write PDF to file
	err = pdfg.WriteFile(cvPdfPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error writing PDF file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("PDF generated successfully: %s\n", cvPdfPath)
}