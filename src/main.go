package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/chromedp/cdproto/emulation"
	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
)

func main() {
	// Suppress default log output to reduce noise
	log.SetOutput(io.Discard)

	// Get current directory
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting current directory: %v\n", err)
		os.Exit(1)
	}

	// Get parent directory (since we're in src/)
	parentDir := filepath.Dir(currentDir)

	// Define paths
	cvHtmlPath := fmt.Sprintf("file://%s/web/index.html?type=pdf", parentDir)
	cvPdfDir := filepath.Join(parentDir, "dispa")
	cvPdfPath := filepath.Join(cvPdfDir, "<PERSON><PERSON>_<PERSON><PERSON>lli_CV.pdf")

	// Create dist directory if it doesn't exist
	if err := os.MkdirAll(cvPdfDir, 0o755); err != nil {
		fmt.Fprintf(os.Stderr, "Error creating dist directory: %v\n", err)
		os.Exit(1)
	}

	// Configure Chrome options to use Brave browser
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.ExecPath("/usr/bin/brave"),
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-web-security", true),
		chromedp.Flag("disable-features", "VizDisplayCompositor"),
	)

	// Create allocator context with error suppression
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	// Create context with custom logger to suppress cookie errors
	ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(func(string, ...interface{}) {}))
	defer cancel()

	// Generate PDF
	var pdfBuffer []byte
	err = chromedp.Run(ctx,
		chromedp.Navigate(cvHtmlPath),
		chromedp.ActionFunc(func(ctx context.Context) error {
			// Emulate screen media type (equivalent to page.emulateMediaType('screen'))
			return emulation.SetEmulatedMedia().WithMedia("screen").Do(ctx)
		}),
		chromedp.WaitReady("body"),
		chromedp.Sleep(2*time.Second), // Wait for network idle (similar to networkidle0)
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = page.PrintToPDF().
				WithPaperWidth(8.27).   // A4 width in inches
				WithPaperHeight(11.7).  // A4 height in inches
				WithMarginTop(0.39).    // 10mm in inches
				WithMarginBottom(0.39). // 10mm in inches
				WithMarginLeft(0.79).   // 20mm in inches
				WithMarginRight(0.79).  // 20mm in inches
				WithScale(0.95).
				WithPrintBackground(true).
				Do(ctx)
			return err
		}),
	)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error generating PDF: %v\n", err)
		os.Exit(1)
	}

	// Write PDF to file
	err = os.WriteFile(cvPdfPath, pdfBuffer, 0o644)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error writing PDF file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("PDF generated successfully: %s\n", cvPdfPath)
}
