package main

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
)

func main() {
	// Get current directory
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting current directory: %v\n", err)
		os.Exit(1)
	}

	// Get parent directory (since we're in src/)
	parentDir := filepath.Dir(currentDir)

	// Define paths
	cvHtmlPath := fmt.Sprintf("file://%s/web/index.html?type=pdf", parentDir)
	cvPdfDir := filepath.Join(parentDir, "pdf")
	cvPdfPath := filepath.Join(cvPdfDir, "Valon_Mulolli_CV.pdf")

	// Create pdf directory if it doesn't exist
	if err := os.MkdirAll(cvPdfDir, 0o755); err != nil {
		fmt.Fprintf(os.Stderr, "Error creating pdf directory: %v\n", err)
		os.Exit(1)
	}

	// Launch browser - Rod will automatically find Chrome/Chromium
	l := launcher.New().
		Headless(true).
		NoSandbox(true)

	defer l.Cleanup()

	url := l.MustLaunch()

	// Create browser instance
	browser := rod.New().ControlURL(url).MustConnect()
	defer browser.MustClose()

	// Create page and navigate
	page := browser.MustPage(cvHtmlPath)

	// Wait for page to load
	page.MustWaitLoad()

	// Wait a bit more for any dynamic content
	time.Sleep(2 * time.Second)

	// Generate PDF
	pdf, err := page.PDF(&proto.PagePrintToPDF{
		PaperWidth:              8.27,  // A4 width in inches
		PaperHeight:             11.7,  // A4 height in inches
		MarginTop:               0.39,  // 10mm in inches
		MarginBottom:            0.39,  // 10mm in inches
		MarginLeft:              0.79,  // 20mm in inches
		MarginRight:             0.79,  // 20mm in inches
		Scale:                   0.95,
		PrintBackground:         true,
		PreferCSSPageSize:       false,
		TransferMode:            proto.PagePrintToPDFTransferModeReturnAsStream,
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error generating PDF: %v\n", err)
		os.Exit(1)
	}

	// Write PDF to file
	err = os.WriteFile(cvPdfPath, pdf, 0o644)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error writing PDF file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("PDF generated successfully: %s\n", cvPdfPath)
}
