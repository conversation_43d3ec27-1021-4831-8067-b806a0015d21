package main

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/Sebas<PERSON>anK<PERSON>/go-wkhtmltopdf"
)

func main() {
	// Get current directory
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting current directory: %v\n", err)
		os.Exit(1)
	}

	// Get parent directory (since we're in src/)
	parentDir := filepath.Dir(currentDir)

	// Define paths
	cvHtmlPath := fmt.Sprintf("file://%s/web/index.html?type=pdf", parentDir)
	cvPdfDir := filepath.Join(parentDir, "pdf")
	cvPdfPath := filepath.Join(cvPdfDir, "Valon_Mulolli_CV.pdf")

	// Create pdf directory if it doesn't exist
	if err := os.MkdirAll(cvPdfDir, 0o755); err != nil {
		fmt.Fprintf(os.<PERSON>derr, "Error creating pdf directory: %v\n", err)
		os.Exit(1)
	}

	// Create new PDF generator
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating PDF generator: %v\n", err)
		os.Exit(1)
	}

	// Set global options
	pdfg.Dpi.Set(300)
	pdfg.Orientation.Set(wkhtmltopdf.OrientationPortrait)
	pdfg.Grayscale.Set(false)

	// Set page options
	page := wkhtmltopdf.NewPage(cvHtmlPath)
	page.FooterRight.Set("[page]")
	page.FooterFontSize.Set(9)
	page.Zoom.Set(0.95)

	// Set margins (in mm)
	page.MarginTop.Set(10)
	page.MarginBottom.Set(10)
	page.MarginLeft.Set(20)
	page.MarginRight.Set(20)

	// Enable print media type and background
	page.PrintMediaType.Set(true)
	page.Background.Set(true)

	// Add page to PDF generator
	pdfg.AddPage(page)

	// Create PDF document in buffer
	err = pdfg.Create()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating PDF: %v\n", err)
		os.Exit(1)
	}

	// Write buffer contents to file
	err = pdfg.WriteFile(cvPdfPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error writing PDF file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("PDF generated successfully: %s\n", cvPdfPath)
}
