/* Print styles for CV */
@media print {
    /* Reset margins and padding for print */
    body {
        margin: 0;
        padding: 20px;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: #fff;
    }

    /* Hide elements that shouldn't print */
    .no-print,
    header .btn,
    footer {
        display: none !important;
    }

    /* Improve link readability */
    a {
        color: #000;
        text-decoration: underline;
    }

    /* Ensure URLs are visible after links */
    a[href^="http"]:after {
        content: " (" attr(href) ")";
        font-size: 0.9em;
        font-weight: normal;
        color: #666;
    }

    /* Page breaks */
    section, article {
        page-break-inside: avoid;
        margin-bottom: 1.5em;
    }

    h1, h2, h3, h4 {
        page-break-after: avoid;
    }

    /* Improve spacing */
    .mb-4, .my-4 {
        margin-bottom: 1rem !important;
    }

    /* Ensure images and code blocks don't break across pages */
    pre, blockquote, img {
        page-break-inside: avoid;
    }

    /* Ensure tables don't break across pages */
    table {
        page-break-inside: auto;
    }
    
    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }
    
    /* Better spacing for lists */
    ul, ol {
        margin-top: 0.5em;
        margin-bottom: 0.5em;
    }
    
    li {
        margin-bottom: 0.25em;
    }
}
