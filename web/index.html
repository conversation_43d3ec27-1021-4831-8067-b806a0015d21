<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Professional CV of Valon Mulolli - Software Developer specializing in Go, Rust, and Data Engineering" />
    <meta name="keywords" content="Valon Mulolli, CV, Resume, Software Developer, Go, Rust, Data Engineering" />
    <meta name="author" content="Valon Mulolli" />
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="Valon Mulolli | CV" />
    <meta property="og:description" content="Software developer transitioning to backend and systems programming. Currently learning Go, Rust, and data engineering technologies." />
    <meta property="og:url" content="https://valon.dev/" />
    <meta property="og:type" content="profile" />
    <meta property="profile:first_name" content="Valon" />
    <meta property="profile:last_name" content="<PERSON><PERSON><PERSON>" />
    <meta property="profile:username" content="valon.dev" />
    
    <title><PERSON><PERSON> | CV</title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <link rel="stylesheet" href="css/github-markdown.min.css" />
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" media="print" href="css/print.css" />
	</head>
</head>
<body class="container my-5 markdown-body markdown-body-website">
    <header class="text-center mb-4">
			<p class="text-center fs-1 mb-2">Valon Mulolli</p>
			<div class="row justify-content-center">
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-github"></i></a>

					<a href="https://github.com/valonmulolli">valonmulolli</a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-solid fa-envelope"></i></i></a>
					<a href="mailto:<EMAIL>"><EMAIL></a>
				</div>
				<div class="col-auto">
					<a href="#" class="icon"><i class="fa-brands fa-linkedin-in"></i></a>
					<a href="https://www.linkedin.com/in/valonmulolli/">Valon Mulolli</a>
				</div>
                <div class="my-2">
                    <i class="fa-solid fa-location-dot" aria-hidden="true"></i>
                    <span>Pristine, Kosovo</span>
                </div>
                <h2 class="h4 mb-3">
                    Software Developer | Learning Go • Rust • Data Engineering
                </h2>
            </div>
        </div>
    </header>

    <main>
        <section aria-labelledby="summary">
            <h2 id="summary" class="my-3">SUMMARY</h2>
            <div class="mb-4">
			I started programming in 2012 during my computer science studies, where Java was part of the curriculum. After a seven-year break, I returned as a self-taught programmer in 2020, initially focusing on JavaScript and web development. I'm now transitioning into backend development and systems programming to expand my technical skills.
	</div>
	<div>
		Learning Backend & Systems: Currently studying Go and Rust to build high-performance applications and understand systems-level programming. Working on personal projects involving microservices architecture, containerization with Docker, and exploring distributed systems concepts. Transitioning from frontend-focused development to backend and infrastructure.
	</div>
	<div>
		Exploring Data Engineering & DevOps: Learning to build data pipelines with Apache Kafka, setting up monitoring with Prometheus and Grafana, and working with databases like PostgreSQL and Redis. Gaining hands-on experience with cloud platforms (AWS, GCP) and modern DevOps practices including CI/CD pipelines.
	</div>
	<div>
		Developer Tools & Environment: Passionate about optimizing development workflow and productivity. Built command-line applications as learning projects, automated personal workflows, and maintain a customized Neovim setup. Daily Linux (Arch Linux) user with strong preference for terminal-based development.
	</div>
	<div>
		Currently focused on deepening knowledge in Rust for systems programming, exploring data engineering concepts, and building practical projects to develop real-world skills. Eager to learn from experienced developers and contribute to meaningful projects while continuing to grow technically.
	</div>

            </div>
        </section>

        <section aria-labelledby="experience">
            <h2 id="experience" class="my-2">EXPERIENCE</h2>

		<article class="mb-4">
                <div class="row d-flex justify-content-between align-items-center">
                    <div class="col col-auto text-start">
                        <a class="fs-6">Central Election Comission of Republic of Kosovo (‘QNR’)</a>
                        <h2>Data entry</h2>
                    </div>
                    <div class="col col-auto text-end">(2011 - 2015)</div>
                </div>

                <div class="mb-1">
                    <div>
                        Organized and managed files with precision for optimal organization. Conducted thorough data analysis, implemented stringent quality control measures, and promptly resolved errors. Actively reported identified issues, thereby contributing to the maintenance of data accuracy. Executed precise and efficient data entry tasks, emphasizing both speed and accuracy. Maintained confidentiality and security while handling sensitive information, adhering strictly to privacy protocols and organizational policies.
                    </div>
                </div>
            </article>

            <article class="mb-4">
                <div class="row d-flex justify-content-between align-items-center">
                    <div class="col col-auto text-start">
                        <a class="fs-6">Remote</a>
                        <h2>Upwork</h2>
                    </div>
                    <div class="col col-auto text-end">2021</div>
                </div>

                <div class="mb-1">
                    <div>
                        For a year as a web developer at Upwork, I worked on remote projects. During this time, I worked on a variety of web development projects, collaborating with clients to offer customized solutions. Coding, troubleshooting, and guaranteeing the proper implementation of web-based initiatives were among my responsibilities. This experience sharpened my abilities in remote communication, project management, and delivering high-quality web solutions to clients all around the world.
                    </div>
                </div>
            </article>
        </section>

        <section aria-labelledby="skills">
            <h2 id="skills" class="my-3">SKILLS</h2>

            <div class="row mt-1 pt-1">
                <div class="col-auto col-skills fw-bold">Programming Languages</div>
                <div class="col">
                    JavaScript/TypeScript, Go, Rust, Python, HTML, CSS, Tailwind CSS
                    <span class="fw-bold">Scripting: </span>
                    Bash, Lua
                </div>
            </div>

            <div class="row">
                <div class="col-auto col-skills fw-bold">Backend & Systems</div>
                <div class="col">Go (Gin framework), Rust, Flask, Microservices</div>
            </div>

            <div class="row">
                <div class="col-auto col-skills fw-bold">Frontend & Web (Experienced)</div>
                <div class="col">Astro, TypeScript, Tailwind CSS, React, Next.js, Modern Web Standards</div>
            </div>

            <div class="row">
                <div class="col-auto col-skills fw-bold">Databases & Storage</div>
                <div class="col">PostgreSQL, Redis, Database design</div>
            </div>

            <div class="row">
                <div class="col-auto col-skills fw-bold">DevOps & Infrastructure</div>
                <div class="col">Docker, Kubernetes, Linux (Arch), Git, GitHub Actions</div>
            </div>

            <div class="row">
                <div class="col-auto col-skills fw-bold">Data Engineering</div>
                <div class="col">Apache Kafka, Prometheus, Grafana</div>
            </div>

            <div class="row">
                <div class="col-auto col-skills fw-bold">Tools & Environment</div>
                <div class="col">Neovim (daily use), Linux (Arch), Command Line Tools, Git workflow</div>
            </div>
        </section>

        <section aria-labelledby="certifications">
            <h2 id="certifications" class="my-3 py-2">CERTIFICATIONS</h2>
            <ul class="list-unstyled">
                <li class="mb-2">
                    <a class="fs-6" href="https://www.hackerrank.com/certificates/4fb46be70fae">JavaScript (Intermediate) Certificate</a>
                    <span class="text-muted">(HackerRank)</span>
                </li>
                <li class="mb-2">
                    <a class="fs-6" href="#">Software Engineer Certificate</a>
                    <span class="text-muted">(HackerRank)</span>
                </li>
            </ul>
        </section>
    <div>
			        </section>

        <section aria-labelledby="projects">
            <h2 id="projects" class="my-3">PROJECTS</h2>
		</div>	

		<div>
			<article class="mb-4">
				<h3>CipherVault</h3>
				<div class="mb-2">
					<ul>
						<li>Description: A learning project - password manager built in Rust to understand systems programming</li>
						<li>Learning Goals: Exploring Rust ownership model, memory safety, and cryptographic concepts</li>
						<li>Features: Basic CLI interface, file-based storage, learning encryption implementation</li>
						<li>Skills Developed: Rust fundamentals, CLI development, understanding security principles</li>
					</ul>
					<li>Technologies: <i class="fas fa-code"></i> Rust, CLI Development</li>
				</div>
				<p>Check out source code on Github: <a href="https://github.com/valonmulolli/ciphervault">CipherVault</a></p>
			</div>
		</article>
			



			<div>
				<article class="mb-4">
					<h2>Task-CLI</h2>
					<p>A learning project - CLI task management application built in Go to understand the language and TUI development</p>
					<ul>
						<li>Learning Goals: Understanding Go syntax, project structure, and terminal UI concepts</li>
						<li>Features: Basic task CRUD operations, simple terminal interface using Bubble Tea framework</li>
						<li>Skills Developed: Go fundamentals, CLI development patterns, terminal UI libraries</li>
						<li>Project Structure: Organized workflow with To Do, In Progress, and Done task sections</li>
						<li>Experience Gained: Working with Go modules, understanding concurrency basics</li>
					</ul>
					<ul>
						<li>Technologies: <i class="fas fa-code"></i> Go, Bubble Tea, CLI Development</li>
					</ul>
					<p>Check out source code on Github: <a href="https://github.com/valonmulolli/task-cli.git">Task-CLI</a></p>
				</article>
		</div>
			<div>
				<article class="mb-4">
					<h3>Personal Blog & Portfolio</h3>
					<div class="mb-2">
						<ul>
							<li>Modern Static Site: Built with Astro to learn modern web development practices</li>
							<li>Content Focus: Documenting my learning journey in Go, Rust, data engineering, and Linux/Neovim</li>
							<li>Tech Stack: Astro, TypeScript, Tailwind CSS, MDX for content authoring</li>
							<li>Features: Dark/light theme, search functionality, responsive design, custom tech badge components</li>
							<li>Deployment: GitHub Pages with automated CI/CD pipeline for learning DevOps concepts</li>
						</ul>
						<li>Technologies: <i class="fas fa-code"></i> Astro, TypeScript, Tailwind CSS, MDX</li>
					</div>
					<p>Check out the blog: <a href="https://valonmulolli.github.io/v470n/">v470n.dev</a></p>
				</article>
			</div>

			<div>
				<article class="mb-4">
					<h3>Learning Projects: Data Engineering & Microservices</h3>
					<div class="mb-2">
						<ul>
							<li>Microservices Exploration: Learning Go-based service architecture with Docker containerization</li>
							<li>Data Pipeline Tutorials: Following Apache Kafka tutorials for understanding data streaming concepts</li>
							<li>Monitoring Setup: Experimenting with Prometheus and Grafana for metrics and visualization</li>
							<li>Database Practice: Working with PostgreSQL and Redis through personal projects and tutorials</li>
							<li>Cloud Learning: Exploring AWS and GCP services through free tier and educational resources</li>
						</ul>
						<li>Technologies: <i class="fas fa-code"></i> Go, Docker, Kafka, Prometheus, Grafana</li>
					</div>
				</article>
			</div>

			<div>
				<article class="mb-4">

					<h3>Previous Projects: Full-Stack Web Development</h3>
					<p>Earlier projects that built my foundation in JavaScript/TypeScript and full-stack development:</p>
					<div class="mb-2">
						<h4>Social Media Clones (Twitter, LinkedIn, Threads)</h4>
						<ul>
							<li>Full-stack learning projects using React Native/Expo for mobile development experience</li>
							<li>Backend APIs built with Express.js and Prisma ORM to understand database integration</li>
							<li>Implemented authentication systems using JWT to learn security concepts</li>
							<li>Features: Real-time updates, file uploads, responsive UI - building practical development skills</li>
							<li>Technologies: TypeScript, React Native, Express.js, Prisma, PostgreSQL</li>
						</ul>
						<p>GitHub: <a href="https://github.com/valonmulolli/twitter-app">Twitter Clone</a> |
						<a href="https://github.com/valonmulolli/LinkedIn-app">LinkedIn Clone</a> |
						<a href="https://github.com/valonmulolli/threads-app">Threads Clone</a></p>
					</div>
				</article>
			</div>

		<script src="./theme.js"></script>
		<script src="./pdf.js"></script>
	        </section>
    </main>

    <footer class="text-center mt-5 mb-3 text-muted">
        <p>Last updated: July 2024</p>
    </footer>
</body>
</html>